// Rust示例脚本内容（将被嵌入到完整的Rust程序中）

// 调用Rust方法进行加法运算
let result1 = add(45, 55);
println!("加法结果: {}", result1);

// 调用Rust方法连接字符串
let result2 = concat_strings("Hello, ", "World from Rust!");
println!("字符串连接结果: {}", result2);

// 获取当前时间
let current_time = get_current_time();
println!("当前时间: {}", current_time);

// 计算数组和
let numbers = [4.0, 8.0, 12.0, 16.0, 20.0];
let sum = sum_array(&numbers);
println!("数组的和为: {}", sum);

// 使用传入的参数
if !user_name.is_empty() {
    println!("传入的用户名: {}", user_name);
}

if user_age > 0 {
    println!("传入的用户年龄: {}", user_age);
}

// 定义一个Rust闭包
let rust_function = |x: i32, y: i32| -> i32 {
    x * x + y * y + 600
};

// 调用Rust闭包
let rust_result = rust_function(9, 12);
println!("Rust函数计算结果: {}", rust_result);

// 使用Rust特有的功能
let mut vector: Vec<i32> = Vec::new();
for i in 0..5 {
    vector.push(i * 20);
}
println!("Rust向量: {:?}", vector);

// 使用HashMap
let mut data_map = HashMap::new();
data_map.insert("red", 255);
data_map.insert("green", 128);
data_map.insert("blue", 64);
println!("Rust哈希映射: {:?}", data_map);

// 使用模式匹配
let number = 42;
match number {
    0 => println!("零"),
    1..=50 => println!("1到50之间的数字: {}", number),
    _ => println!("其他数字: {}", number),
}
