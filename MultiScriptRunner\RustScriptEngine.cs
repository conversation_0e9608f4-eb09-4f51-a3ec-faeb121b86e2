using System.Diagnostics;
using System.Text;

namespace MultiScriptRunner;

public class RustScriptEngine : IScriptEngine
{
    public string EngineType => "Rust";

    public object? Execute(string script, Dictionary<string, object>? parameters = null)
    {
        try
        {
            // 创建临时Rust文件
            var tempFile = Path.GetTempFileName() + ".rs";
            
            // 构建完整的Rust程序
            var fullScript = BuildRustScript(script, parameters);
            File.WriteAllText(tempFile, fullScript, Encoding.UTF8);

            var processInfo = new ProcessStartInfo
            {
                FileName = "rustc",
                Arguments = $"\"{tempFile}\" -o \"{tempFile}.exe\"",
                RedirectStandardOutput = true,
                RedirectStandardError = true,
                UseShellExecute = false,
                CreateNoWindow = true,
                StandardOutputEncoding = Encoding.UTF8,
                StandardErrorEncoding = Encoding.UTF8
            };

            // 编译Rust代码
            using var compileProcess = Process.Start(processInfo);
            if (compileProcess != null)
            {
                compileProcess.WaitForExit();
                
                if (compileProcess.ExitCode != 0)
                {
                    var error = compileProcess.StandardError.ReadToEnd();
                    throw new InvalidOperationException($"Rust编译失败: {error}");
                }
            }

            // 运行编译后的程序
            var runProcessInfo = new ProcessStartInfo
            {
                FileName = $"{tempFile}.exe",
                RedirectStandardOutput = true,
                RedirectStandardError = true,
                UseShellExecute = false,
                CreateNoWindow = true,
                StandardOutputEncoding = Encoding.UTF8,
                StandardErrorEncoding = Encoding.UTF8
            };

            using var runProcess = Process.Start(runProcessInfo);
            if (runProcess != null)
            {
                var output = runProcess.StandardOutput.ReadToEnd();
                var error = runProcess.StandardError.ReadToEnd();
                
                runProcess.WaitForExit();

                if (!string.IsNullOrEmpty(output))
                {
                    Console.WriteLine($"[Rust] {output.Trim()}");
                }

                if (!string.IsNullOrEmpty(error))
                {
                    Console.WriteLine($"[Rust Error] {error.Trim()}");
                }

                // 清理临时文件
                try 
                { 
                    File.Delete(tempFile); 
                    File.Delete($"{tempFile}.exe");
                    File.Delete($"{tempFile}.pdb");
                } 
                catch { }

                return output.Trim();
            }

            return null;
        }
        catch (Exception ex)
        {
            throw new InvalidOperationException($"Rust执行错误: {ex.Message}", ex);
        }
    }

    public object? ExecuteFile(string filePath, Dictionary<string, object>? parameters = null)
    {
        if (!File.Exists(filePath))
            throw new FileNotFoundException($"脚本文件不存在: {filePath}");

        var script = File.ReadAllText(filePath, Encoding.UTF8);
        return Execute(script, parameters);
    }

    public void RegisterMethod(string name, Delegate method)
    {
        // Rust脚本不支持直接注册C#方法
        Console.WriteLine($"[Rust] 警告: Rust脚本不支持直接注册C#方法 '{name}'");
    }

    public void RegisterObject(string name, object obj)
    {
        // Rust脚本不支持直接注册C#对象
        Console.WriteLine($"[Rust] 警告: Rust脚本不支持直接注册C#对象 '{name}'");
    }

    private string BuildRustScript(string userScript, Dictionary<string, object>? parameters)
    {
        var sb = new StringBuilder();
        
        // 添加基本的use语句
        sb.AppendLine("use std::collections::HashMap;");
        sb.AppendLine();

        // 添加辅助函数
        sb.AppendLine("// 辅助函数");
        sb.AppendLine("fn add(a: i32, b: i32) -> i32 {");
        sb.AppendLine("    println!(\"[Rust] 执行加法运算: {} + {}\", a, b);");
        sb.AppendLine("    a + b");
        sb.AppendLine("}");
        sb.AppendLine();
        
        sb.AppendLine("fn concat_strings(str1: &str, str2: &str) -> String {");
        sb.AppendLine("    println!(\"[Rust] 连接字符串: '{}' + '{}'\", str1, str2);");
        sb.AppendLine("    format!(\"{}{}\", str1, str2)");
        sb.AppendLine("}");
        sb.AppendLine();

        sb.AppendLine("fn get_current_time() -> String {");
        sb.AppendLine("    // 简化的时间获取");
        sb.AppendLine("    \"2025-06-07 22:00:00\".to_string()");
        sb.AppendLine("}");
        sb.AppendLine();

        sb.AppendLine("fn sum_array(numbers: &[f64]) -> f64 {");
        sb.AppendLine("    let sum: f64 = numbers.iter().sum();");
        sb.AppendLine("    println!(\"[Rust] 计算数组和: {:?} = {}\", numbers, sum);");
        sb.AppendLine("    sum");
        sb.AppendLine("}");
        sb.AppendLine();

        // 主函数
        sb.AppendLine("fn main() {");
        sb.AppendLine("    println!(\"[Rust] === Rust脚本开始执行 ===\");");
        sb.AppendLine();

        // 添加参数作为变量
        if (parameters != null)
        {
            sb.AppendLine("    // 传入的参数");
            foreach (var param in parameters)
            {
                var value = ConvertToRustValue(param.Value);
                var rustType = GetRustType(param.Value);
                sb.AppendLine($"    let {param.Key}: {rustType} = {value};");
            }
            sb.AppendLine();
        }
        
        // 插入用户脚本
        sb.AppendLine("    // 用户脚本开始");
        sb.AppendLine(IndentCode(userScript, "    "));
        sb.AppendLine("    // 用户脚本结束");
        sb.AppendLine();
        
        sb.AppendLine("    println!(\"[Rust] === Rust脚本执行完成 ===\");");
        sb.AppendLine("}");

        return sb.ToString();
    }

    private string ConvertToRustValue(object value)
    {
        return value switch
        {
            string s => $"\"{s}\"",
            int i => i.ToString(),
            double d => d.ToString("F1"),
            float f => f.ToString("F1"),
            bool b => b.ToString().ToLower(),
            _ => $"\"{value}\""
        };
    }

    private string GetRustType(object value)
    {
        return value switch
        {
            string => "&str",
            int => "i32",
            double => "f64",
            float => "f32",
            bool => "bool",
            _ => "&str"
        };
    }

    private string IndentCode(string code, string indent)
    {
        var lines = code.Split('\n');
        return string.Join('\n', lines.Select(line => indent + line.TrimEnd()));
    }
}
