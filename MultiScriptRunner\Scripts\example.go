// Go示例脚本内容（将被嵌入到完整的Go程序中）

// 调用Go方法进行加法运算
result1 := add(40, 50)
fmt.Printf("加法结果: %d\n", result1)

// 调用Go方法连接字符串
result2 := concatStrings("Hello, ", "World from Go!")
fmt.Printf("字符串连接结果: %s\n", result2)

// 获取当前时间
currentTime := getCurrentTime()
fmt.Printf("当前时间: %s\n", currentTime)

// 计算数组和
numbers := []float64{3.5, 7.0, 10.5, 14.0, 17.5}
sum := sumArray(numbers)
fmt.Printf("数组的和为: %f\n", sum)

// 使用传入的参数
if userName != "" {
    fmt.Printf("传入的用户名: %s\n", userName)
}

if userAge > 0 {
    fmt.Printf("传入的用户年龄: %d\n", userAge)
}

// 定义一个Go函数
goFunction := func(x, y int) int {
    return x*y + 500
}

// 调用Go函数
goResult := goFunction(8, 9)
fmt.Printf("Go函数计算结果: %d\n", goResult)

// 使用Go特有的功能
slice := make([]int, 5)
for i := range slice {
    slice[i] = i * 10
}
fmt.Printf("Go切片: %v\n", slice)

// 使用map
dataMap := make(map[string]int)
dataMap["apple"] = 5
dataMap["banana"] = 3
dataMap["orange"] = 8
fmt.Printf("Go映射: %v\n", dataMap)
