C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\ref\Microsoft.CSharp.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\ref\Microsoft.VisualBasic.Core.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\ref\Microsoft.VisualBasic.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\ref\Microsoft.Win32.Primitives.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\ref\Microsoft.Win32.Registry.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\ref\System.AppContext.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\ref\System.Buffers.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\ref\System.Collections.Concurrent.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\ref\System.Collections.Immutable.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\ref\System.Collections.NonGeneric.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\ref\System.Collections.Specialized.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\ref\System.Collections.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\ref\System.ComponentModel.Annotations.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\ref\System.ComponentModel.DataAnnotations.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\ref\System.ComponentModel.EventBasedAsync.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\ref\System.ComponentModel.Primitives.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\ref\System.ComponentModel.TypeConverter.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\ref\System.ComponentModel.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\ref\System.Configuration.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\ref\System.Console.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\ref\System.Core.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\ref\System.Data.Common.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\ref\System.Data.DataSetExtensions.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\ref\System.Data.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\ref\System.Diagnostics.Contracts.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\ref\System.Diagnostics.Debug.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\ref\System.Diagnostics.DiagnosticSource.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\ref\System.Diagnostics.FileVersionInfo.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\ref\System.Diagnostics.Process.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\ref\System.Diagnostics.StackTrace.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\ref\System.Diagnostics.TextWriterTraceListener.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\ref\System.Diagnostics.Tools.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\ref\System.Diagnostics.TraceSource.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\ref\System.Diagnostics.Tracing.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\ref\System.Drawing.Primitives.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\ref\System.Drawing.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\ref\System.Dynamic.Runtime.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\ref\System.Formats.Asn1.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\ref\System.Formats.Tar.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\ref\System.Globalization.Calendars.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\ref\System.Globalization.Extensions.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\ref\System.Globalization.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\ref\System.IO.Compression.Brotli.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\ref\System.IO.Compression.FileSystem.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\ref\System.IO.Compression.ZipFile.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\ref\System.IO.Compression.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\ref\System.IO.FileSystem.AccessControl.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\ref\System.IO.FileSystem.DriveInfo.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\ref\System.IO.FileSystem.Primitives.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\ref\System.IO.FileSystem.Watcher.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\ref\System.IO.FileSystem.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\ref\System.IO.IsolatedStorage.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\ref\System.IO.MemoryMappedFiles.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\ref\System.IO.Pipelines.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\ref\System.IO.Pipes.AccessControl.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\ref\System.IO.Pipes.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\ref\System.IO.UnmanagedMemoryStream.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\ref\System.IO.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\ref\System.Linq.Expressions.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\ref\System.Linq.Parallel.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\ref\System.Linq.Queryable.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\ref\System.Linq.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\ref\System.Memory.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\ref\System.Net.Http.Json.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\ref\System.Net.Http.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\ref\System.Net.HttpListener.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\ref\System.Net.Mail.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\ref\System.Net.NameResolution.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\ref\System.Net.NetworkInformation.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\ref\System.Net.Ping.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\ref\System.Net.Primitives.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\ref\System.Net.Quic.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\ref\System.Net.Requests.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\ref\System.Net.Security.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\ref\System.Net.ServicePoint.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\ref\System.Net.Sockets.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\ref\System.Net.WebClient.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\ref\System.Net.WebHeaderCollection.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\ref\System.Net.WebProxy.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\ref\System.Net.WebSockets.Client.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\ref\System.Net.WebSockets.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\ref\System.Net.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\ref\System.Numerics.Vectors.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\ref\System.Numerics.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\ref\System.ObjectModel.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\ref\System.Reflection.DispatchProxy.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\ref\System.Reflection.Emit.ILGeneration.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\ref\System.Reflection.Emit.Lightweight.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\ref\System.Reflection.Emit.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\ref\System.Reflection.Extensions.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\ref\System.Reflection.Metadata.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\ref\System.Reflection.Primitives.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\ref\System.Reflection.TypeExtensions.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\ref\System.Reflection.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\ref\System.Resources.Reader.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\ref\System.Resources.ResourceManager.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\ref\System.Resources.Writer.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\ref\System.Runtime.CompilerServices.Unsafe.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\ref\System.Runtime.CompilerServices.VisualC.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\ref\System.Runtime.Extensions.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\ref\System.Runtime.Handles.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\ref\System.Runtime.InteropServices.JavaScript.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\ref\System.Runtime.InteropServices.RuntimeInformation.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\ref\System.Runtime.InteropServices.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\ref\System.Runtime.Intrinsics.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\ref\System.Runtime.Loader.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\ref\System.Runtime.Numerics.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\ref\System.Runtime.Serialization.Formatters.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\ref\System.Runtime.Serialization.Json.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\ref\System.Runtime.Serialization.Primitives.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\ref\System.Runtime.Serialization.Xml.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\ref\System.Runtime.Serialization.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\ref\System.Runtime.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\ref\System.Security.AccessControl.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\ref\System.Security.Claims.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\ref\System.Security.Cryptography.Algorithms.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\ref\System.Security.Cryptography.Cng.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\ref\System.Security.Cryptography.Csp.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\ref\System.Security.Cryptography.Encoding.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\ref\System.Security.Cryptography.OpenSsl.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\ref\System.Security.Cryptography.Primitives.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\ref\System.Security.Cryptography.X509Certificates.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\ref\System.Security.Cryptography.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\ref\System.Security.Principal.Windows.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\ref\System.Security.Principal.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\ref\System.Security.SecureString.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\ref\System.Security.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\ref\System.ServiceModel.Web.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\ref\System.ServiceProcess.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\ref\System.Text.Encoding.CodePages.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\ref\System.Text.Encoding.Extensions.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\ref\System.Text.Encoding.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\ref\System.Text.Encodings.Web.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\ref\System.Text.Json.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\ref\System.Text.RegularExpressions.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\ref\System.Threading.Channels.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\ref\System.Threading.Overlapped.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\ref\System.Threading.Tasks.Dataflow.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\ref\System.Threading.Tasks.Extensions.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\ref\System.Threading.Tasks.Parallel.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\ref\System.Threading.Tasks.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\ref\System.Threading.Thread.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\ref\System.Threading.ThreadPool.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\ref\System.Threading.Timer.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\ref\System.Threading.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\ref\System.Transactions.Local.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\ref\System.Transactions.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\ref\System.ValueTuple.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\ref\System.Web.HttpUtility.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\ref\System.Web.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\ref\System.Windows.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\ref\System.Xml.Linq.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\ref\System.Xml.ReaderWriter.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\ref\System.Xml.Serialization.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\ref\System.Xml.XDocument.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\ref\System.Xml.XPath.XDocument.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\ref\System.Xml.XPath.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\ref\System.Xml.XmlDocument.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\ref\System.Xml.XmlSerializer.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\ref\System.Xml.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\ref\System.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\ref\WindowsBase.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\ref\mscorlib.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\ref\netstandard.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\runtimes\unix\lib\net9.0\Modules\Microsoft.PowerShell.Host\Microsoft.PowerShell.Host.psd1
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\runtimes\unix\lib\net9.0\Modules\Microsoft.PowerShell.Management\Microsoft.PowerShell.Management.psd1
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\runtimes\unix\lib\net9.0\Modules\Microsoft.PowerShell.Security\Microsoft.PowerShell.Security.psd1
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\runtimes\unix\lib\net9.0\Modules\Microsoft.PowerShell.Utility\Microsoft.PowerShell.Utility.psd1
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\runtimes\win\lib\net9.0\Modules\CimCmdlets\CimCmdlets.psd1
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\runtimes\win\lib\net9.0\Modules\Microsoft.PowerShell.Diagnostics\Diagnostics.format.ps1xml
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\runtimes\win\lib\net9.0\Modules\Microsoft.PowerShell.Diagnostics\Event.format.ps1xml
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\runtimes\win\lib\net9.0\Modules\Microsoft.PowerShell.Diagnostics\GetEvent.types.ps1xml
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\runtimes\win\lib\net9.0\Modules\Microsoft.PowerShell.Diagnostics\Microsoft.PowerShell.Diagnostics.psd1
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\runtimes\win\lib\net9.0\Modules\Microsoft.PowerShell.Host\Microsoft.PowerShell.Host.psd1
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\runtimes\win\lib\net9.0\Modules\Microsoft.PowerShell.Management\Microsoft.PowerShell.Management.psd1
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\runtimes\win\lib\net9.0\Modules\Microsoft.PowerShell.Security\Microsoft.PowerShell.Security.psd1
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\runtimes\win\lib\net9.0\Modules\Microsoft.PowerShell.Security\Security.types.ps1xml
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\runtimes\win\lib\net9.0\Modules\Microsoft.PowerShell.Utility\Microsoft.PowerShell.Utility.psd1
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\runtimes\win\lib\net9.0\Modules\Microsoft.WSMan.Management\Microsoft.WSMan.Management.psd1
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\runtimes\win\lib\net9.0\Modules\Microsoft.WSMan.Management\WSMan.format.ps1xml
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\runtimes\win\lib\net9.0\Modules\PSDiagnostics\PSDiagnostics.psd1
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\runtimes\win\lib\net9.0\Modules\PSDiagnostics\PSDiagnostics.psm1
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\MultiScriptRunner.exe
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\MultiScriptRunner.deps.json
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\MultiScriptRunner.runtimeconfig.json
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\MultiScriptRunner.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\MultiScriptRunner.pdb
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\Acornima.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\Microsoft.Dynamic.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\Microsoft.Scripting.Metadata.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\Microsoft.Scripting.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\Humanizer.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\IronPython.Modules.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\IronPython.SQLite.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\IronPython.Wpf.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\IronPython.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\Jint.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\Json.More.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\JsonPointer.Net.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\JsonSchema.Net.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\KeraLua.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\Markdig.Signed.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\Microsoft.ApplicationInsights.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\Microsoft.Bcl.AsyncInterfaces.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\Microsoft.CodeAnalysis.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\Microsoft.CodeAnalysis.CSharp.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\Microsoft.CodeAnalysis.CSharp.Scripting.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\Microsoft.CodeAnalysis.Scripting.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\Microsoft.Extensions.ObjectPool.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\Microsoft.PowerShell.MarkdownRender.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\Microsoft.Win32.Registry.AccessControl.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\Microsoft.Win32.SystemEvents.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\Mono.Unix.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\Newtonsoft.Json.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\NLua.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\System.CodeDom.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\System.ComponentModel.Composition.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\System.ComponentModel.Composition.Registration.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\System.Configuration.ConfigurationManager.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\System.Data.Odbc.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\System.Data.OleDb.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\System.Data.SqlClient.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\System.Diagnostics.DiagnosticSource.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\System.Diagnostics.EventLog.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\System.Diagnostics.PerformanceCounter.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\System.DirectoryServices.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\System.DirectoryServices.AccountManagement.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\System.DirectoryServices.Protocols.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\System.Drawing.Common.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\System.Private.Windows.Core.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\System.IO.Packaging.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\System.IO.Ports.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\System.Management.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\System.Net.Http.WinHttpHandler.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\System.Private.ServiceModel.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\System.Reflection.Context.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\System.Runtime.Caching.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\System.Security.Cryptography.Pkcs.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\System.Security.Cryptography.ProtectedData.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\System.Security.Cryptography.Xml.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\System.Security.Permissions.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\System.ServiceModel.Duplex.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\System.ServiceModel.Http.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\System.ServiceModel.NetTcp.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\System.ServiceModel.Primitives.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\System.ServiceModel.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\System.ServiceModel.Security.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\System.ServiceModel.Syndication.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\System.ServiceProcess.ServiceController.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\System.Speech.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\System.Text.Encoding.CodePages.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\System.Text.Encodings.Web.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\System.Threading.AccessControl.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\System.Web.Services.Description.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\System.Windows.Extensions.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\cs\Microsoft.CodeAnalysis.resources.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\de\Microsoft.CodeAnalysis.resources.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\es\Microsoft.CodeAnalysis.resources.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\fr\Microsoft.CodeAnalysis.resources.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\it\Microsoft.CodeAnalysis.resources.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\ja\Microsoft.CodeAnalysis.resources.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\ko\Microsoft.CodeAnalysis.resources.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\pl\Microsoft.CodeAnalysis.resources.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\pt-BR\Microsoft.CodeAnalysis.resources.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\ru\Microsoft.CodeAnalysis.resources.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\tr\Microsoft.CodeAnalysis.resources.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\zh-Hans\Microsoft.CodeAnalysis.resources.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\zh-Hant\Microsoft.CodeAnalysis.resources.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\cs\Microsoft.CodeAnalysis.CSharp.resources.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\de\Microsoft.CodeAnalysis.CSharp.resources.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\es\Microsoft.CodeAnalysis.CSharp.resources.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\fr\Microsoft.CodeAnalysis.CSharp.resources.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\it\Microsoft.CodeAnalysis.CSharp.resources.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\ja\Microsoft.CodeAnalysis.CSharp.resources.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\ko\Microsoft.CodeAnalysis.CSharp.resources.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\pl\Microsoft.CodeAnalysis.CSharp.resources.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\pt-BR\Microsoft.CodeAnalysis.CSharp.resources.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\ru\Microsoft.CodeAnalysis.CSharp.resources.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\tr\Microsoft.CodeAnalysis.CSharp.resources.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\zh-Hans\Microsoft.CodeAnalysis.CSharp.resources.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\zh-Hant\Microsoft.CodeAnalysis.CSharp.resources.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\cs\Microsoft.CodeAnalysis.CSharp.Scripting.resources.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\de\Microsoft.CodeAnalysis.CSharp.Scripting.resources.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\es\Microsoft.CodeAnalysis.CSharp.Scripting.resources.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\fr\Microsoft.CodeAnalysis.CSharp.Scripting.resources.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\it\Microsoft.CodeAnalysis.CSharp.Scripting.resources.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\ja\Microsoft.CodeAnalysis.CSharp.Scripting.resources.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\ko\Microsoft.CodeAnalysis.CSharp.Scripting.resources.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\pl\Microsoft.CodeAnalysis.CSharp.Scripting.resources.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\pt-BR\Microsoft.CodeAnalysis.CSharp.Scripting.resources.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\ru\Microsoft.CodeAnalysis.CSharp.Scripting.resources.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\tr\Microsoft.CodeAnalysis.CSharp.Scripting.resources.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\zh-Hans\Microsoft.CodeAnalysis.CSharp.Scripting.resources.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\zh-Hant\Microsoft.CodeAnalysis.CSharp.Scripting.resources.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\cs\Microsoft.CodeAnalysis.Scripting.resources.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\de\Microsoft.CodeAnalysis.Scripting.resources.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\es\Microsoft.CodeAnalysis.Scripting.resources.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\fr\Microsoft.CodeAnalysis.Scripting.resources.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\it\Microsoft.CodeAnalysis.Scripting.resources.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\ja\Microsoft.CodeAnalysis.Scripting.resources.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\ko\Microsoft.CodeAnalysis.Scripting.resources.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\pl\Microsoft.CodeAnalysis.Scripting.resources.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\pt-BR\Microsoft.CodeAnalysis.Scripting.resources.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\ru\Microsoft.CodeAnalysis.Scripting.resources.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\tr\Microsoft.CodeAnalysis.Scripting.resources.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\zh-Hans\Microsoft.CodeAnalysis.Scripting.resources.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\zh-Hant\Microsoft.CodeAnalysis.Scripting.resources.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\cs\System.Private.ServiceModel.resources.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\de\System.Private.ServiceModel.resources.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\es\System.Private.ServiceModel.resources.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\fr\System.Private.ServiceModel.resources.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\it\System.Private.ServiceModel.resources.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\ja\System.Private.ServiceModel.resources.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\ko\System.Private.ServiceModel.resources.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\pl\System.Private.ServiceModel.resources.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\pt-BR\System.Private.ServiceModel.resources.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\ru\System.Private.ServiceModel.resources.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\tr\System.Private.ServiceModel.resources.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\zh-Hans\System.Private.ServiceModel.resources.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\zh-Hant\System.Private.ServiceModel.resources.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\cs\System.Web.Services.Description.resources.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\de\System.Web.Services.Description.resources.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\es\System.Web.Services.Description.resources.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\fr\System.Web.Services.Description.resources.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\it\System.Web.Services.Description.resources.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\ja\System.Web.Services.Description.resources.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\ko\System.Web.Services.Description.resources.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\pl\System.Web.Services.Description.resources.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\pt-BR\System.Web.Services.Description.resources.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\ru\System.Web.Services.Description.resources.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\tr\System.Web.Services.Description.resources.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\zh-Hans\System.Web.Services.Description.resources.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\zh-Hant\System.Web.Services.Description.resources.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\runtimes\android-arm\native\liblua54.so
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\runtimes\android-arm64\native\liblua54.so
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\runtimes\android-x64\native\liblua54.so
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\runtimes\android-x86\native\liblua54.so
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\runtimes\ios\native\liblua54.framework\Info.plist
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\runtimes\ios\native\liblua54.framework\liblua54
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\runtimes\linux-arm64\native\liblua54.so
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\runtimes\linux-x64\native\liblua54.so
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\runtimes\maccatalyst\native\liblua54.framework\Info.plist
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\runtimes\maccatalyst\native\liblua54.framework\liblua54
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\runtimes\osx\native\liblua54.dylib
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\runtimes\tvos\native\liblua54.framework\Info.plist
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\runtimes\tvos\native\liblua54.framework\liblua54
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\runtimes\win-arm\native\lua54.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\runtimes\win-arm64\native\lua54.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\runtimes\win-x64\native\lua54.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\runtimes\win-x86\native\lua54.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\runtimes\win\lib\net9.0\Microsoft.Management.Infrastructure.CimCmdlets.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\runtimes\unix\lib\netstandard1.6\Microsoft.Management.Infrastructure.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\runtimes\win-arm64\lib\netstandard1.6\microsoft.management.infrastructure.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\runtimes\win-arm64\lib\netstandard1.6\microsoft.management.infrastructure.native.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\runtimes\win-arm64\native\microsoft.management.infrastructure.native.unmanaged.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\runtimes\win-x64\lib\netstandard1.6\microsoft.management.infrastructure.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\runtimes\win-x64\lib\netstandard1.6\microsoft.management.infrastructure.native.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\runtimes\win-x64\native\microsoft.management.infrastructure.native.unmanaged.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\runtimes\win-x86\lib\netstandard1.6\microsoft.management.infrastructure.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\runtimes\win-x86\lib\netstandard1.6\microsoft.management.infrastructure.native.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\runtimes\win-x86\native\microsoft.management.infrastructure.native.unmanaged.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\runtimes\win\lib\net9.0\Microsoft.PowerShell.Commands.Diagnostics.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\runtimes\unix\lib\net9.0\Microsoft.PowerShell.Commands.Management.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\runtimes\win\lib\net9.0\Microsoft.PowerShell.Commands.Management.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\runtimes\unix\lib\net9.0\Microsoft.PowerShell.Commands.Utility.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\runtimes\win\lib\net9.0\Microsoft.PowerShell.Commands.Utility.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\runtimes\unix\lib\net9.0\Microsoft.PowerShell.ConsoleHost.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\runtimes\win\lib\net9.0\Microsoft.PowerShell.ConsoleHost.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\runtimes\win\lib\net9.0\Microsoft.PowerShell.CoreCLR.Eventing.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\runtimes\linux-arm\native\libpsl-native.so
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\runtimes\linux-arm64\native\libpsl-native.so
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\runtimes\linux-musl-x64\native\libpsl-native.so
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\runtimes\linux-x64\native\libpsl-native.so
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\runtimes\osx\native\libpsl-native.dylib
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\runtimes\win-arm\native\PowerShell.Core.Instrumentation.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\runtimes\win-arm\native\pwrshplugin.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\runtimes\win-arm64\native\PowerShell.Core.Instrumentation.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\runtimes\win-arm64\native\pwrshplugin.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\runtimes\win-x64\native\PowerShell.Core.Instrumentation.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\runtimes\win-x64\native\pwrshplugin.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\runtimes\win-x86\native\PowerShell.Core.Instrumentation.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\runtimes\win-x86\native\pwrshplugin.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\runtimes\unix\lib\net9.0\Microsoft.PowerShell.SDK.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\runtimes\win\lib\net9.0\Microsoft.PowerShell.SDK.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\runtimes\unix\lib\net9.0\Microsoft.PowerShell.Security.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\runtimes\win\lib\net9.0\Microsoft.PowerShell.Security.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\runtimes\win-arm64\lib\net5.0\getfilesiginforedistwrapper.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\runtimes\win-arm64\native\getfilesiginforedist.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\runtimes\win-x64\lib\net5.0\getfilesiginforedistwrapper.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\runtimes\win-x64\native\getfilesiginforedist.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\runtimes\win-x86\lib\net5.0\getfilesiginforedistwrapper.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\runtimes\win-x86\native\getfilesiginforedist.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\runtimes\win\lib\net9.0\Microsoft.Win32.Registry.AccessControl.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\runtimes\win\lib\net9.0\Microsoft.Win32.SystemEvents.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\runtimes\win\lib\net9.0\Microsoft.WSMan.Management.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\runtimes\win\lib\net9.0\Microsoft.WSMan.Runtime.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\runtimes\android-arm\native\libMono.Unix.so
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\runtimes\android-arm64\native\libMono.Unix.so
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\runtimes\android-x64\native\libMono.Unix.so
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\runtimes\android-x86\native\libMono.Unix.so
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\runtimes\ios-arm\native\libMono.Unix.a
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\runtimes\ios-arm64\native\libMono.Unix.a
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\runtimes\ios-armv7s\native\libMono.Unix.a
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\runtimes\iossimulator-arm64\native\libMono.Unix.a
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\runtimes\iossimulator-x64\native\libMono.Unix.a
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\runtimes\linux-arm\native\libMono.Unix.so
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\runtimes\linux-arm64\native\libMono.Unix.so
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\runtimes\linux-x64\native\libMono.Unix.so
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\runtimes\maccatalyst-arm64\native\libMono.Unix.a
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\runtimes\maccatalyst-x64\native\libMono.Unix.a
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\runtimes\osx-arm64\native\libMono.Unix.dylib
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\runtimes\osx-x64\native\libMono.Unix.dylib
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\runtimes\tvos-arm64\native\libMono.Unix.a
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\runtimes\tvossimulator-x64\native\libMono.Unix.a
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\runtimes\android-arm\native\libSystem.IO.Ports.Native.so
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\runtimes\android-arm64\native\libSystem.IO.Ports.Native.so
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\runtimes\android-x64\native\libSystem.IO.Ports.Native.so
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\runtimes\android-x86\native\libSystem.IO.Ports.Native.so
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\runtimes\linux-arm\native\libSystem.IO.Ports.Native.so
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\runtimes\linux-arm64\native\libSystem.IO.Ports.Native.so
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\runtimes\linux-bionic-arm64\native\libSystem.IO.Ports.Native.so
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\runtimes\linux-bionic-x64\native\libSystem.IO.Ports.Native.so
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\runtimes\linux-musl-arm\native\libSystem.IO.Ports.Native.so
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\runtimes\linux-musl-arm64\native\libSystem.IO.Ports.Native.so
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\runtimes\linux-musl-x64\native\libSystem.IO.Ports.Native.so
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\runtimes\linux-x64\native\libSystem.IO.Ports.Native.so
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\runtimes\maccatalyst-arm64\native\libSystem.IO.Ports.Native.dylib
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\runtimes\maccatalyst-x64\native\libSystem.IO.Ports.Native.dylib
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\runtimes\osx-arm64\native\libSystem.IO.Ports.Native.dylib
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\runtimes\osx-x64\native\libSystem.IO.Ports.Native.dylib
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\runtimes\win-arm64\native\sni.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\runtimes\win-x64\native\sni.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\runtimes\win-x86\native\sni.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\runtimes\unix\lib\net9.0\System.Data.Odbc.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\runtimes\win\lib\net9.0\System.Data.Odbc.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\runtimes\win\lib\net9.0\System.Data.OleDb.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\runtimes\unix\lib\netcoreapp2.1\System.Data.SqlClient.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\runtimes\win\lib\netcoreapp2.1\System.Data.SqlClient.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\runtimes\win\lib\net9.0\System.Diagnostics.EventLog.Messages.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\runtimes\win\lib\net9.0\System.Diagnostics.EventLog.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\runtimes\win\lib\net9.0\System.Diagnostics.PerformanceCounter.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\runtimes\win\lib\net9.0\System.DirectoryServices.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\runtimes\win\lib\net9.0\System.DirectoryServices.AccountManagement.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\runtimes\linux\lib\net9.0\System.DirectoryServices.Protocols.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\runtimes\osx\lib\net9.0\System.DirectoryServices.Protocols.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\runtimes\win\lib\net9.0\System.DirectoryServices.Protocols.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\runtimes\unix\lib\net9.0\System.IO.Ports.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\runtimes\win\lib\net9.0\System.IO.Ports.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\runtimes\win\lib\net9.0\System.Management.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\runtimes\unix\lib\net9.0\System.Management.Automation.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\runtimes\win\lib\net9.0\System.Management.Automation.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\runtimes\win\lib\net9.0\System.Net.Http.WinHttpHandler.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\runtimes\win\lib\net9.0\System.Runtime.Caching.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\runtimes\win\lib\net9.0\System.Security.Cryptography.Pkcs.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\runtimes\win\lib\net9.0\System.ServiceProcess.ServiceController.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\runtimes\win\lib\net9.0\System.Speech.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\runtimes\win\lib\net9.0\System.Text.Encoding.CodePages.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\runtimes\browser\lib\net9.0\System.Text.Encodings.Web.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\runtimes\win\lib\net9.0\System.Threading.AccessControl.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\bin\Debug\net9.0\runtimes\win\lib\net9.0\System.Windows.Extensions.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\obj\Debug\net9.0\MultiScriptRunner.csproj.AssemblyReference.cache
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\obj\Debug\net9.0\MultiScriptRunner.GeneratedMSBuildEditorConfig.editorconfig
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\obj\Debug\net9.0\MultiScriptRunner.AssemblyInfoInputs.cache
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\obj\Debug\net9.0\MultiScriptRunner.AssemblyInfo.cs
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\obj\Debug\net9.0\MultiScriptRunner.csproj.CoreCompileInputs.cache
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\obj\Debug\net9.0\MultiScr.C5AAA7E5.Up2Date
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\obj\Debug\net9.0\MultiScriptRunner.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\obj\Debug\net9.0\refint\MultiScriptRunner.dll
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\obj\Debug\net9.0\MultiScriptRunner.pdb
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\obj\Debug\net9.0\MultiScriptRunner.genruntimeconfig.cache
C:\Users\<USER>\Desktop\csharpScript\MultiScriptRunner\obj\Debug\net9.0\ref\MultiScriptRunner.dll
