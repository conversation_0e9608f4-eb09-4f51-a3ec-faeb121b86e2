# IronPython示例脚本
print("=== IronPython脚本开始执行 ===")

# 调用C#方法进行加法运算
result1 = add(30, 40)
print("加法结果:", result1)

# 调用C#方法连接字符串
result2 = concatStrings("Hello, ", "World from IronPython!")
print("字符串连接结果:", result2)

# 获取当前时间
current_time = getCurrentTime()
print("当前时间:", current_time)

# 计算数组和
numbers = [2.5, 5.0, 7.5, 10.0, 12.5]
sum_result = sumArray(numbers)
print("数组", numbers, "的和为:", sum_result)

# 创建数据对象
data_obj = createDataObject("孙七", 40, "<EMAIL>")
print("创建了数据对象")

# 使用传入的参数
try:
    if userName:
        print("传入的用户名:", userName)
except NameError:
    pass

try:
    if userAge:
        print("传入的用户年龄:", userAge)
except NameError:
    pass

# 定义一个Python函数
def python_function(x, y):
    return x ** 2 + y ** 2 + 300

# 调用Python函数
python_result = python_function(4, 5)
print("Python函数计算结果:", python_result)

# 使用Python特有的功能
import random
random_numbers = [random.randint(1, 100) for _ in range(5)]
print("随机数列表:", random_numbers)

print("=== IronPython脚本执行完成 ===")

# 返回结果
{
    "message": "IronPython脚本执行完成",
    "timestamp": current_time,
    "calculatedSum": sum_result,
    "pythonCalculation": python_result,
    "randomNumbers": random_numbers
}
