# Python示例脚本
print("=== Python脚本开始执行 ===")

# 调用C#方法进行加法运算
result1 = add(20, 30)
print(f"加法结果: {result1}")

# 调用C#方法连接字符串
result2 = concatStrings("Hello, ", "World from Python!")
print(f"字符串连接结果: {result2}")

# 获取当前时间
current_time = getCurrentTime()
print(f"当前时间: {current_time}")

# 计算数组和
numbers = [1.1, 2.2, 3.3, 4.4, 5.5]
sum_result = sumArray(numbers)
print(f"数组 {numbers} 的和为: {sum_result}")

# 创建数据对象
data_obj = createDataObject("王五", 28, "<EMAIL>")
print("创建了数据对象")

# 使用传入的参数
try:
    if 'userName' in locals():
        print(f"传入的用户名: {userName}")
except NameError:
    pass

try:
    if 'userAge' in locals():
        print(f"传入的用户年龄: {userAge}")
except NameError:
    pass

# 定义一个Python函数
def python_function(x, y):
    return x ** y + 50

# 调用Python函数
python_result = python_function(3, 4)
print(f"Python函数计算结果: {python_result}")

# 使用Python特有的功能
import math
math_result = math.sqrt(sum_result)
print(f"数组和的平方根: {math_result}")

print("=== Python脚本执行完成 ===")

# 返回结果（Python中最后一个表达式的值会被返回）
{
    "message": "Python脚本执行完成",
    "timestamp": current_time,
    "calculatedSum": sum_result,
    "pythonCalculation": python_result,
    "mathResult": math_result
}
