# PowerShell示例脚本
Write-Host "=== PowerShell脚本开始执行 ==="

# 调用C#方法进行加法运算
$result1 = add 25 35
Write-Host "加法结果: $result1"

# 调用C#方法连接字符串
$result2 = concatStrings "Hello, " "World from PowerShell!"
Write-Host "字符串连接结果: $result2"

# 获取当前时间
$currentTime = getCurrentTime
Write-Host "当前时间: $currentTime"

# 计算数组和
$numbers = @(3.0, 6.0, 9.0, 12.0, 15.0)
$sum = sumArray $numbers
Write-Host "数组的和为: $sum"

# 创建数据对象
$dataObj = createDataObject "赵六" 35 "<EMAIL>"
Write-Host "创建了数据对象"

# 使用传入的参数
if ($userName) {
    Write-Host "传入的用户名: $userName"
}

if ($userAge) {
    Write-Host "传入的用户年龄: $userAge"
}

# 定义一个PowerShell函数
function PowerShellFunction($x, $y) {
    return $x * $y + 200
}

# 调用PowerShell函数
$psResult = PowerShellFunction 7 8
Write-Host "PowerShell函数计算结果: $psResult"

# 使用PowerShell特有的功能
$processes = Get-Process | Select-Object -First 3 Name, Id
Write-Host "前3个进程:"
$processes | ForEach-Object { Write-Host "  $($_.Name) (ID: $($_.Id))" }

Write-Host "=== PowerShell脚本执行完成 ==="

# 返回结果
@{
    message = "PowerShell脚本执行完成"
    timestamp = $currentTime
    calculatedSum = $sum
    psCalculation = $psResult
    processCount = $processes.Count
}
