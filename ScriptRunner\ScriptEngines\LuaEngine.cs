using NLua;

namespace ScriptRunner.ScriptEngines;

public class LuaEngine : IScriptEngine
{
    private readonly Lua _lua;

    public string EngineType => "Lua";

    public LuaEngine()
    {
        _lua = new Lua();
        
        // 注册控制台输出方法
        _lua.RegisterFunction("print", this, typeof(LuaEngine).GetMethod(nameof(LuaPrint)));
    }

    public static void LuaPrint(params object[] args)
    {
        var message = string.Join(" ", args.Select(arg => arg?.ToString() ?? "nil"));
        Console.WriteLine($"[Lua] {message}");
    }

    public object? Execute(string script, Dictionary<string, object>? parameters = null)
    {
        try
        {
            // 设置参数
            if (parameters != null)
            {
                foreach (var param in parameters)
                {
                    _lua[param.Key] = param.Value;
                }
            }

            var results = _lua.DoString(script);
            return results?.Length > 0 ? results[0] : null;
        }
        catch (Exception ex)
        {
            throw new InvalidOperationException($"Lua执行错误: {ex.Message}", ex);
        }
    }

    public object? ExecuteFile(string filePath, Dictionary<string, object>? parameters = null)
    {
        if (!File.Exists(filePath))
            throw new FileNotFoundException($"脚本文件不存在: {filePath}");

        try
        {
            // 设置参数
            if (parameters != null)
            {
                foreach (var param in parameters)
                {
                    _lua[param.Key] = param.Value;
                }
            }

            var results = _lua.DoFile(filePath);
            return results?.Length > 0 ? results[0] : null;
        }
        catch (Exception ex)
        {
            throw new InvalidOperationException($"Lua文件执行错误: {ex.Message}", ex);
        }
    }

    public void RegisterMethod(string name, Delegate method)
    {
        _lua.RegisterFunction(name, method.Target, method.Method);
    }

    public void RegisterObject(string name, object obj)
    {
        _lua[name] = obj;
    }

    public void Dispose()
    {
        _lua?.Dispose();
    }
}
