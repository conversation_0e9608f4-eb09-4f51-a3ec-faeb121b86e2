namespace ScriptRunner.ScriptEngines;

public interface IScriptEngine
{
    /// <summary>
    /// 执行脚本代码
    /// </summary>
    /// <param name="script">脚本代码</param>
    /// <param name="parameters">传递给脚本的参数</param>
    /// <returns>脚本执行结果</returns>
    object? Execute(string script, Dictionary<string, object>? parameters = null);

    /// <summary>
    /// 执行脚本文件
    /// </summary>
    /// <param name="filePath">脚本文件路径</param>
    /// <param name="parameters">传递给脚本的参数</param>
    /// <returns>脚本执行结果</returns>
    object? ExecuteFile(string filePath, Dictionary<string, object>? parameters = null);

    /// <summary>
    /// 注册C#方法供脚本调用
    /// </summary>
    /// <param name="name">方法名称</param>
    /// <param name="method">方法委托</param>
    void RegisterMethod(string name, Delegate method);

    /// <summary>
    /// 注册C#对象供脚本调用
    /// </summary>
    /// <param name="name">对象名称</param>
    /// <param name="obj">对象实例</param>
    void RegisterObject(string name, object obj);

    /// <summary>
    /// 获取脚本引擎类型
    /// </summary>
    string EngineType { get; }
}
