{"version": 2, "dgSpecHash": "5zt5k9b1R5Y=", "success": true, "projectFilePath": "C:\\Users\\<USER>\\Desktop\\csharpScript\\ScriptRunner\\ScriptRunner.csproj", "expectedPackageFiles": ["C:\\Users\\<USER>\\.nuget\\packages\\acornima\\1.1.0\\acornima.1.1.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\jint\\4.1.0\\jint.4.1.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\keralua\\1.4.4\\keralua.1.4.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\nlua\\1.7.5\\nlua.1.7.5.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\pythonnet\\3.0.5\\pythonnet.3.0.5.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.netcore.app.ref\\9.0.3\\microsoft.netcore.app.ref.9.0.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.windowsdesktop.app.ref\\9.0.3\\microsoft.windowsdesktop.app.ref.9.0.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.app.ref\\9.0.3\\microsoft.aspnetcore.app.ref.9.0.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.netcore.app.host.win-x64\\9.0.3\\microsoft.netcore.app.host.win-x64.9.0.3.nupkg.sha512"], "logs": []}