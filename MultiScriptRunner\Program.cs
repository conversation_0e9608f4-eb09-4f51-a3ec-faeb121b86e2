﻿namespace MultiScriptRunner;

class Program
{
    static void Main(string[] args)
    {
        Console.WriteLine("=== .NET Core 9 多语言脚本执行器 ===");
        Console.WriteLine();

        var scriptManager = new ScriptManager();

        // 显示支持的脚本引擎
        Console.WriteLine("支持的脚本引擎:");
        foreach (var engine in scriptManager.GetSupportedEngines())
        {
            Console.WriteLine($"  - {engine}");
        }
        Console.WriteLine();

        // 准备传递给脚本的参数
        var parameters = new Dictionary<string, object>
        {
            ["userName"] = "测试用户",
            ["userAge"] = 25
        };

        try
        {
            // 执行JavaScript脚本
            Console.WriteLine(">>> 执行JavaScript脚本 <<<");
            var jsResult = scriptManager.ExecuteScriptFile("Scripts/example.js", parameters);
            Console.WriteLine($"JavaScript执行结果: {jsResult}");
            Console.WriteLine();

            // 执行Lua脚本
            Console.WriteLine(">>> 执行Lua脚本 <<<");
            try
            {
                var luaResult = scriptManager.ExecuteScriptFile("Scripts/example.lua", parameters);
                Console.WriteLine($"Lua执行结果: {luaResult}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Lua脚本执行失败: {ex.Message}");
            }
            Console.WriteLine();

            // 执行PowerShell脚本
            Console.WriteLine(">>> 执行PowerShell脚本 <<<");
            try
            {
                var psResult = scriptManager.ExecuteScriptFile("Scripts/example.ps1", parameters);
                Console.WriteLine($"PowerShell执行结果: {psResult}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"PowerShell脚本执行失败: {ex.Message}");
            }
            Console.WriteLine();

            // 执行IronPython脚本
            Console.WriteLine(">>> 执行IronPython脚本 <<<");
            try
            {
                var pyResult = scriptManager.ExecuteScriptFile("Scripts/example.py", parameters);
                Console.WriteLine($"IronPython执行结果: {pyResult}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"IronPython脚本执行失败: {ex.Message}");
            }
            Console.WriteLine();

            // 执行C#脚本
            Console.WriteLine(">>> 执行C#脚本 <<<");
            try
            {
                var csResult = scriptManager.ExecuteScriptFile("Scripts/example.csx", parameters);
                Console.WriteLine($"C#脚本执行结果: {csResult}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"C#脚本执行失败: {ex.Message}");
            }
            Console.WriteLine();

            // 执行Batch脚本
            Console.WriteLine(">>> 执行Batch脚本 <<<");
            try
            {
                var batResult = scriptManager.ExecuteScriptFile("Scripts/example.bat", parameters);
                Console.WriteLine($"Batch脚本执行结果: {batResult}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Batch脚本执行失败: {ex.Message}");
            }
            Console.WriteLine();

            // 执行Go脚本
            Console.WriteLine(">>> 执行Go脚本 <<<");
            try
            {
                var goResult = scriptManager.ExecuteScriptFile("Scripts/example.go", parameters);
                Console.WriteLine($"Go脚本执行结果: {goResult}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Go脚本执行失败: {ex.Message}");
            }
            Console.WriteLine();

            // 执行Rust脚本
            Console.WriteLine(">>> 执行Rust脚本 <<<");
            try
            {
                var rustResult = scriptManager.ExecuteScriptFile("Scripts/example.rs", parameters);
                Console.WriteLine($"Rust脚本执行结果: {rustResult}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Rust脚本执行失败: {ex.Message}");
            }
            Console.WriteLine();

        }
        catch (Exception ex)
        {
            Console.WriteLine($"执行错误: {ex.Message}");
        }

        Console.WriteLine("按任意键退出...");
        Console.ReadKey();
    }
}
