using ScriptRunner.ScriptEngines;
using ScriptRunner.CSharpMethods;

namespace ScriptRunner;

public class ScriptManager
{
    private readonly Dictionary<string, IScriptEngine> _engines;

    public ScriptManager()
    {
        _engines = new Dictionary<string, IScriptEngine>();
        InitializeEngines();
    }

    private void InitializeEngines()
    {
        // 初始化JavaScript引擎
        var jsEngine = new ScriptEngines.JavaScriptEngine();
        RegisterCommonMethods(jsEngine);
        _engines["js"] = jsEngine;
        _engines["javascript"] = jsEngine;

        // 初始化Lua引擎
        var luaEngine = new ScriptEngines.LuaEngine();
        RegisterCommonMethods(luaEngine);
        _engines["lua"] = luaEngine;

        // 初始化Python引擎
        try
        {
            var pythonEngine = new ScriptEngines.PythonEngine();
            RegisterCommonMethods(pythonEngine);
            _engines["py"] = pythonEngine;
            _engines["python"] = pythonEngine;
        }
        catch (Exception ex)
        {
            Console.WriteLine($"警告: Python引擎初始化失败: {ex.Message}");
        }
    }

    private void RegisterCommonMethods(IScriptEngine engine)
    {
        // 注册C#方法
        engine.RegisterMethod("add", new Func<int, int, int>(ApiMethods.Add));
        engine.RegisterMethod("concatStrings", new Func<string, string, string>(ApiMethods.ConcatStrings));
        engine.RegisterMethod("getCurrentTime", new Func<string>(ApiMethods.GetCurrentTime));
        engine.RegisterMethod("sumArray", new Func<double[], double>(ApiMethods.SumArray));
        engine.RegisterMethod("writeToFile", new Action<string, string>(ApiMethods.WriteToFile));
        engine.RegisterMethod("readFromFile", new Func<string, string>(ApiMethods.ReadFromFile));
        engine.RegisterMethod("createDataObject", new Func<string, int, string, object>(ApiMethods.CreateDataObject));

        // 注册ApiMethods类的实例
        engine.RegisterObject("api", new ApiMethods());
    }

    public object? ExecuteScript(string scriptType, string script, Dictionary<string, object>? parameters = null)
    {
        var engine = GetEngine(scriptType);
        return engine.Execute(script, parameters);
    }

    public object? ExecuteScriptFile(string filePath, Dictionary<string, object>? parameters = null)
    {
        var extension = Path.GetExtension(filePath).ToLower();
        var scriptType = extension switch
        {
            ".js" => "javascript",
            ".lua" => "lua",
            ".py" => "python",
            _ => throw new NotSupportedException($"不支持的脚本文件类型: {extension}")
        };

        var engine = GetEngine(scriptType);
        return engine.ExecuteFile(filePath, parameters);
    }

    public IScriptEngine GetEngine(string scriptType)
    {
        if (!_engines.TryGetValue(scriptType.ToLower(), out var engine))
        {
            throw new NotSupportedException($"不支持的脚本类型: {scriptType}");
        }
        return engine;
    }

    public IEnumerable<string> GetSupportedEngines()
    {
        return _engines.Keys.Distinct();
    }

    public void RegisterMethodToEngine(string scriptType, string methodName, Delegate method)
    {
        var engine = GetEngine(scriptType);
        engine.RegisterMethod(methodName, method);
    }

    public void RegisterObjectToEngine(string scriptType, string objectName, object obj)
    {
        var engine = GetEngine(scriptType);
        engine.RegisterObject(objectName, obj);
    }

    public void RegisterMethodToAllEngines(string methodName, Delegate method)
    {
        foreach (var engine in _engines.Values.Distinct())
        {
            engine.RegisterMethod(methodName, method);
        }
    }

    public void RegisterObjectToAllEngines(string objectName, object obj)
    {
        foreach (var engine in _engines.Values.Distinct())
        {
            engine.RegisterObject(objectName, obj);
        }
    }
}
