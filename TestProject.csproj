<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>Exe</OutputType>
    <TargetFramework>net9.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="IronPython" Version="3.4.2" />
    <PackageReference Include="Jint" Version="4.1.0" />
    <PackageReference Include="Microsoft.CodeAnalysis.CSharp.Scripting" Version="4.14.0" />
    <PackageReference Include="Microsoft.PowerShell.SDK" Version="7.5.1" />
    <PackageReference Include="NLua" Version="1.7.5" />
  </ItemGroup>

  <ItemGroup>
    <Compile Include="*.cs" Exclude="TestRunner.cs" />
  </ItemGroup>

</Project>
