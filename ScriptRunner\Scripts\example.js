// JavaScript示例脚本
console.log("=== JavaScript脚本开始执行 ===");

// 调用C#方法进行加法运算
var result1 = add(10, 20);
console.log("加法结果: " + result1);

// 调用C#方法连接字符串
var result2 = concatStrings("Hello, ", "World from JavaScript!");
console.log("字符串连接结果: " + result2);

// 获取当前时间
var currentTime = getCurrentTime();
console.log("当前时间: " + currentTime);

// 计算数组和
var numbers = [1.5, 2.5, 3.5, 4.5, 5.5];
var sum = sumArray(numbers);
console.log("数组 [" + numbers.join(", ") + "] 的和为: " + sum);

// 创建数据对象
var dataObj = createDataObject("张三", 25, "<EMAIL>");
console.log("创建的数据对象: " + JSON.stringify(dataObj));

// 使用传入的参数
if (typeof userName !== 'undefined') {
    console.log("传入的用户名: " + userName);
}

if (typeof userAge !== 'undefined') {
    console.log("传入的用户年龄: " + userAge);
}

// 返回一个结果
var finalResult = {
    message: "JavaScript脚本执行完成",
    timestamp: currentTime,
    calculatedSum: sum
};

console.log("=== JavaScript脚本执行完成 ===");

// 返回结果
finalResult;
