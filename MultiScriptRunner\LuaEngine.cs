using NLua;
using System.Text;

namespace MultiScriptRunner;

public class LuaEngine : IScriptEngine
{
    private readonly Lua _lua;

    public string EngineType => "Lua";

    public LuaEngine()
    {
        _lua = new Lua();

        // 设置控制台编码
        try
        {
            Console.OutputEncoding = Encoding.UTF8;
            Console.InputEncoding = Encoding.UTF8;
        }
        catch
        {
            // 如果设置编码失败，继续使用默认编码
        }

        // 注册控制台输出方法
        _lua.RegisterFunction("print", this, typeof(LuaEngine).GetMethod(nameof(LuaPrint)));
    }

    public static void LuaPrint(params object[] args)
    {
        var parts = new List<string>();
        foreach (var arg in args)
        {
            if (arg == null)
            {
                parts.Add("nil");
            }
            else
            {
                var str = arg.ToString();
                // 直接使用字符串，不进行复杂的编码转换
                parts.Add(str ?? "nil");
            }
        }

        var message = string.Join(" ", parts);

        // 使用简单的方式输出，避免编码问题
        try
        {
            Console.WriteLine($"[Lua] {message}");
        }
        catch
        {
            // 如果输出失败，使用ASCII安全的方式
            Console.WriteLine("[Lua] <output encoding error>");
        }
    }

    public object? Execute(string script, Dictionary<string, object>? parameters = null)
    {
        try
        {
            // 设置参数
            if (parameters != null)
            {
                foreach (var param in parameters)
                {
                    _lua[param.Key] = param.Value;
                }
            }

            var results = _lua.DoString(script);
            return results?.Length > 0 ? results[0] : null;
        }
        catch (Exception ex)
        {
            throw new InvalidOperationException($"Lua执行错误: {ex.Message}", ex);
        }
    }

    public object? ExecuteFile(string filePath, Dictionary<string, object>? parameters = null)
    {
        if (!File.Exists(filePath))
            throw new FileNotFoundException($"脚本文件不存在: {filePath}");

        try
        {
            // 设置参数
            if (parameters != null)
            {
                foreach (var param in parameters)
                {
                    _lua[param.Key] = param.Value;
                }
            }

            // 使用UTF-8编码读取文件内容，然后执行
            var scriptContent = File.ReadAllText(filePath, Encoding.UTF8);
            var results = _lua.DoString(scriptContent);
            return results?.Length > 0 ? results[0] : null;
        }
        catch (Exception ex)
        {
            throw new InvalidOperationException($"Lua文件执行错误: {ex.Message}", ex);
        }
    }

    public void RegisterMethod(string name, Delegate method)
    {
        _lua.RegisterFunction(name, method.Target, method.Method);
    }

    public void RegisterObject(string name, object obj)
    {
        _lua[name] = obj;
    }

    public void Dispose()
    {
        _lua?.Dispose();
    }
}
