@echo off
echo === Batch脚本开始执行 ===

echo 用户名: %userName%
echo 用户年龄: %userAge%

echo 当前日期和时间: %date% %time%
echo 当前目录: %cd%
echo 计算机名: %COMPUTERNAME%
echo 用户域: %USERDOMAIN%

echo 执行简单的数学运算:
set /a result1=10+20
echo 10 + 20 = %result1%

set /a result2=50*3
echo 50 * 3 = %result2%

echo 列出当前目录的文件:
dir /b *.* | findstr /v "^$"

echo 系统信息:
echo 操作系统: %OS%
echo 处理器架构: %PROCESSOR_ARCHITECTURE%
echo 处理器数量: %NUMBER_OF_PROCESSORS%

echo === Batch脚本执行完成 ===
