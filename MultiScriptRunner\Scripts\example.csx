// C#脚本示例
WriteLine("=== C#脚本开始执行 ===");

// 调用C#方法进行加法运算
var result1 = add(35, 45);
WriteLine($"加法结果: {result1}");

// 调用C#方法连接字符串
var result2 = concatStrings("Hello, ", "World from C# Script!");
WriteLine($"字符串连接结果: {result2}");

// 获取当前时间
var currentTime = getCurrentTime();
WriteLine($"当前时间: {currentTime}");

// 计算数组和
var numbers = new double[] { 4.0, 8.0, 12.0, 16.0, 20.0 };
var sum = sumArray(numbers);
WriteLine($"数组 [{string.Join(", ", numbers)}] 的和为: {sum}");

// 创建数据对象
var dataObj = createDataObject("周八", 45, "<EMAIL>");
WriteLine("创建了数据对象");

// 使用传入的参数
if (userName != null)
{
    WriteLine($"传入的用户名: {userName}");
}

if (userAge != null)
{
    WriteLine($"传入的用户年龄: {userAge}");
}

// 定义一个C#函数
Func<int, int, int> csharpFunction = (x, y) => x * x + y * y + 400;

// 调用C#函数
var csResult = csharpFunction(6, 8);
WriteLine($"C#函数计算结果: {csResult}");

// 使用C#特有的功能
var guid = Guid.NewGuid();
WriteLine($"生成的GUID: {guid}");

var dateTime = DateTime.Now;
WriteLine($"当前详细时间: {dateTime:yyyy-MM-dd HH:mm:ss.fff}");

// 使用LINQ
var evenNumbers = Enumerable.Range(1, 10).Where(x => x % 2 == 0).ToArray();
WriteLine($"1-10中的偶数: [{string.Join(", ", evenNumbers)}]");

WriteLine("=== C#脚本执行完成 ===");

// 返回结果
new {
    message = "C#脚本执行完成",
    timestamp = currentTime,
    calculatedSum = sum,
    csharpCalculation = csResult,
    generatedGuid = guid.ToString(),
    evenNumbers = evenNumbers
}
