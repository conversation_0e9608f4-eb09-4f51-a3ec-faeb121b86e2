using System;

namespace MultiScriptRunner
{
    class TestRunner
    {
        static void Main(string[] args)
        {
            Console.WriteLine("=== 多语言脚本执行器测试 ===");
            
            try
            {
                var scriptManager = new ScriptManager();
                
                Console.WriteLine("\n支持的脚本引擎:");
                var engines = scriptManager.GetSupportedEngines();
                foreach (var engine in engines)
                {
                    Console.WriteLine($"- {engine}");
                }
                
                Console.WriteLine("\n=== JavaScript 测试 ===");
                var jsResult = scriptManager.Execute("js", "console.log('Hello from JavaScript!'); return 42;");
                Console.WriteLine($"JavaScript 结果: {jsResult}");
                
                Console.WriteLine("\n=== Lua 测试 ===");
                var luaResult = scriptManager.Execute("lua", "print('Hello from Lua!'); return 123");
                Console.WriteLine($"Lua 结果: {luaResult}");
                
                Console.WriteLine("\n测试完成!");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"测试失败: {ex.Message}");
                Console.WriteLine($"详细错误: {ex}");
            }
            
            Console.WriteLine("\n按任意键退出...");
            Console.ReadKey();
        }
    }
}
