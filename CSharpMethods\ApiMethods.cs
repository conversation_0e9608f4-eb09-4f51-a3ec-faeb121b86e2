namespace ScriptRunner.CSharpMethods;

public class ApiMethods
{
    /// <summary>
    /// 简单的加法运算
    /// </summary>
    public static int Add(int a, int b)
    {
        Console.WriteLine($"[C#] 执行加法运算: {a} + {b}");
        return a + b;
    }

    /// <summary>
    /// 字符串连接
    /// </summary>
    public static string ConcatStrings(string str1, string str2)
    {
        Console.WriteLine($"[C#] 连接字符串: '{str1}' + '{str2}'");
        return str1 + str2;
    }

    /// <summary>
    /// 获取当前时间
    /// </summary>
    public static string GetCurrentTime()
    {
        var now = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
        Console.WriteLine($"[C#] 获取当前时间: {now}");
        return now;
    }

    /// <summary>
    /// 计算数组和
    /// </summary>
    public static double SumArray(double[] numbers)
    {
        var sum = numbers.Sum();
        Console.WriteLine($"[C#] 计算数组和: [{string.Join(", ", numbers)}] = {sum}");
        return sum;
    }

    /// <summary>
    /// 写入文件
    /// </summary>
    public static void WriteToFile(string fileName, string content)
    {
        Console.WriteLine($"[C#] 写入文件: {fileName}");
        File.WriteAllText(fileName, content);
    }

    /// <summary>
    /// 读取文件
    /// </summary>
    public static string ReadFromFile(string fileName)
    {
        Console.WriteLine($"[C#] 读取文件: {fileName}");
        if (File.Exists(fileName))
        {
            return File.ReadAllText(fileName);
        }
        return string.Empty;
    }

    /// <summary>
    /// 发送HTTP GET请求（简化版）
    /// </summary>
    public static async Task<string> HttpGet(string url)
    {
        Console.WriteLine($"[C#] 发送HTTP GET请求: {url}");
        using var client = new HttpClient();
        try
        {
            var response = await client.GetStringAsync(url);
            return response;
        }
        catch (Exception ex)
        {
            return $"请求失败: {ex.Message}";
        }
    }

    /// <summary>
    /// 创建一个简单的数据对象
    /// </summary>
    public static object CreateDataObject(string name, int age, string email)
    {
        Console.WriteLine($"[C#] 创建数据对象: {name}, {age}, {email}");
        return new
        {
            Name = name,
            Age = age,
            Email = email,
            CreatedAt = DateTime.Now
        };
    }
}
