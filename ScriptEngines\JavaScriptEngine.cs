using Jint;
using Jint.Native;

namespace ScriptRunner.ScriptEngines;

public class JavaScriptEngine : IScriptEngine
{
    private readonly Engine _engine;

    public string EngineType => "JavaScript";

    public JavaScriptEngine()
    {
        _engine = new Engine();
        
        // 注册控制台输出方法
        _engine.SetValue("console", new
        {
            log = new Action<object>(obj => Console.WriteLine($"[JS] {obj}"))
        });
    }

    public object? Execute(string script, Dictionary<string, object>? parameters = null)
    {
        try
        {
            // 设置参数
            if (parameters != null)
            {
                foreach (var param in parameters)
                {
                    _engine.SetValue(param.Key, param.Value);
                }
            }

            var result = _engine.Evaluate(script);
            return ConvertJsValue(result);
        }
        catch (Exception ex)
        {
            throw new InvalidOperationException($"JavaScript执行错误: {ex.Message}", ex);
        }
    }

    public object? ExecuteFile(string filePath, Dictionary<string, object>? parameters = null)
    {
        if (!File.Exists(filePath))
            throw new FileNotFoundException($"脚本文件不存在: {filePath}");

        var script = File.ReadAllText(filePath);
        return Execute(script, parameters);
    }

    public void RegisterMethod(string name, Delegate method)
    {
        _engine.SetValue(name, method);
    }

    public void RegisterObject(string name, object obj)
    {
        _engine.SetValue(name, obj);
    }

    private static object? ConvertJsValue(JsValue jsValue)
    {
        if (jsValue.IsBoolean())
            return jsValue.AsBoolean();
        if (jsValue.IsNumber())
            return jsValue.AsNumber();
        if (jsValue.IsString())
            return jsValue.AsString();
        if (jsValue.IsNull() || jsValue.IsUndefined())
            return null;
        
        return jsValue.ToObject();
    }
}
