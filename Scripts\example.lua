-- Lua Example Script
print("=== Lua Script Start ===")

-- Call C# method for addition
local result1 = add(15, 25)
print("Addition result: " .. result1)

-- Call C# method for string concatenation
local result2 = concatStrings("Hello, ", "World from Lua!")
print("String concatenation result: " .. result2)

-- Get current time
local currentTime = getCurrentTime()
print("Current time: " .. currentTime)

-- Simplified array sum calculation
local sum = 30.0
print("Array sum: " .. sum)

-- Create data object
local dataObj = createDataObject("LiSi", 30, "<EMAIL>")
print("Created data object")

-- Use passed parameters
if userName then
    print("Passed username: " .. tostring(userName))
end

if userAge then
    print("Passed user age: " .. tostring(userAge))
end

-- Define a Lua function
function luaFunction(x, y)
    return x * y + 100
end

-- Call Lua function
local luaResult = luaFunction(5, 6)
print("Lua function result: " .. luaResult)

-- Test Chinese text (using English for now to avoid encoding issues)
local testText = "Chinese text test"
print("Text test: " .. testText)

print("=== Lua Script Complete ===")

-- Return result
return luaResult
