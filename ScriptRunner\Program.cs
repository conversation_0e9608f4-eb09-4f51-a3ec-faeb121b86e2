﻿namespace ScriptRunner;

class Program
{
    static void Main(string[] args)
    {
        Console.WriteLine("=== .NET Core 9 多语言脚本执行器 ===");
        Console.WriteLine();

        var scriptManager = new ScriptManager();

        // 显示支持的脚本引擎
        Console.WriteLine("支持的脚本引擎:");
        foreach (var engine in scriptManager.GetSupportedEngines())
        {
            Console.WriteLine($"  - {engine}");
        }
        Console.WriteLine();

        // 准备传递给脚本的参数
        var parameters = new Dictionary<string, object>
        {
            ["userName"] = "测试用户",
            ["userAge"] = 25
        };

        try
        {
            // 执行JavaScript脚本
            Console.WriteLine(">>> 执行JavaScript脚本 <<<");
            var jsResult = scriptManager.ExecuteScriptFile("Scripts/example.js", parameters);
            Console.WriteLine($"JavaScript执行结果: {jsResult}");
            Console.WriteLine();

            // 执行Lua脚本
            Console.WriteLine(">>> 执行Lua脚本 <<<");
            var luaResult = scriptManager.ExecuteScriptFile("Scripts/example.lua", parameters);
            Console.WriteLine($"Lua执行结果: {luaResult}");
            Console.WriteLine();

            // 执行Python脚本
            Console.WriteLine(">>> 执行Python脚本 <<<");
            try
            {
                var pythonResult = scriptManager.ExecuteScriptFile("Scripts/example.py", parameters);
                Console.WriteLine($"Python执行结果: {pythonResult}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Python脚本执行失败: {ex.Message}");
            }
            Console.WriteLine();

            // 演示直接执行脚本代码
            Console.WriteLine(">>> 直接执行脚本代码示例 <<<");

            // JavaScript代码示例
            var jsCode = @"
                console.log('直接执行的JavaScript代码');
                var result = add(100, 200);
                console.log('计算结果: ' + result);
                result;
            ";
            var directJsResult = scriptManager.ExecuteScript("javascript", jsCode);
            Console.WriteLine($"直接执行JavaScript结果: {directJsResult}");
            Console.WriteLine();

            // Lua代码示例
            var luaCode = @"
                print('直接执行的Lua代码')
                local result = add(50, 75)
                print('计算结果: ' .. result)
                return result
            ";
            var directLuaResult = scriptManager.ExecuteScript("lua", luaCode);
            Console.WriteLine($"直接执行Lua结果: {directLuaResult}");
            Console.WriteLine();

        }
        catch (Exception ex)
        {
            Console.WriteLine($"执行错误: {ex.Message}");
        }

        Console.WriteLine("按任意键退出...");
        Console.ReadKey();
    }
}
