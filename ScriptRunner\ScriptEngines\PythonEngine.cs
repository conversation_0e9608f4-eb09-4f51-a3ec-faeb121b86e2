namespace ScriptRunner.ScriptEngines;

public class PythonEngine : IScriptEngine
{
    public string EngineType => "Python";

    public object? Execute(string script, Dictionary<string, object>? parameters = null)
    {
        // 暂时禁用Python支持，因为Python.NET配置复杂
        throw new NotSupportedException("Python引擎暂时不可用。请安装并配置Python环境。");
    }

    public object? ExecuteFile(string filePath, Dictionary<string, object>? parameters = null)
    {
        throw new NotSupportedException("Python引擎暂时不可用。请安装并配置Python环境。");
    }

    public void RegisterMethod(string name, Delegate method)
    {
        // Python引擎暂时不可用
    }

    public void RegisterObject(string name, object obj)
    {
        // Python引擎暂时不可用
    }
}
