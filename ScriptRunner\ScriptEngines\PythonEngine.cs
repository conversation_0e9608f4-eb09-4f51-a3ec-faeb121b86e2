using Python.Runtime;

namespace ScriptRunner.ScriptEngines;

public class PythonEngine : IScriptEngine, IDisposable
{
    private readonly PyScope _scope;
    private static bool _initialized = false;

    public string EngineType => "Python";

    public PythonEngine()
    {
        InitializePython();
        _scope = Py.CreateScope();
        
        // 添加print重定向
        _scope.Exec(@"
import sys
from io import StringIO

class CSharpPrint:
    def write(self, text):
        if text.strip():
            print(f'[Python] {text.strip()}')
    def flush(self):
        pass

# 重定向print输出
sys.stdout = CSharpPrint()
");
    }

    private static void InitializePython()
    {
        if (!_initialized)
        {
            try
            {
                // 尝试初始化Python运行时
                if (!PythonEngine.IsInitialized)
                {
                    PythonEngine.Initialize();
                }
                _initialized = true;
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"Python初始化失败: {ex.Message}. 请确保已安装Python。", ex);
            }
        }
    }

    public object? Execute(string script, Dictionary<string, object>? parameters = null)
    {
        try
        {
            using (Py.GIL())
            {
                // 设置参数
                if (parameters != null)
                {
                    foreach (var param in parameters)
                    {
                        _scope.Set(param.Key, param.Value.ToPython());
                    }
                }

                var result = _scope.Eval(script);
                return result?.As<object>();
            }
        }
        catch (Exception ex)
        {
            throw new InvalidOperationException($"Python执行错误: {ex.Message}", ex);
        }
    }

    public object? ExecuteFile(string filePath, Dictionary<string, object>? parameters = null)
    {
        if (!File.Exists(filePath))
            throw new FileNotFoundException($"脚本文件不存在: {filePath}");

        var script = File.ReadAllText(filePath);
        return Execute(script, parameters);
    }

    public void RegisterMethod(string name, Delegate method)
    {
        using (Py.GIL())
        {
            _scope.Set(name, method.ToPython());
        }
    }

    public void RegisterObject(string name, object obj)
    {
        using (Py.GIL())
        {
            _scope.Set(name, obj.ToPython());
        }
    }

    public void Dispose()
    {
        _scope?.Dispose();
    }
}
