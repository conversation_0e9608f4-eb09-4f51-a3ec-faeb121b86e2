using System.Diagnostics;
using System.Text;

namespace MultiScriptRunner;

public class GoScriptEngine : IScriptEngine
{
    public string EngineType => "Go";

    public object? Execute(string script, Dictionary<string, object>? parameters = null)
    {
        try
        {
            // 创建临时Go文件
            var tempFile = Path.GetTempFileName() + ".go";
            
            // 构建完整的Go程序
            var fullScript = BuildGoScript(script, parameters);
            File.WriteAllText(tempFile, fullScript, Encoding.UTF8);

            var processInfo = new ProcessStartInfo
            {
                FileName = "go",
                Arguments = $"run \"{tempFile}\"",
                RedirectStandardOutput = true,
                RedirectStandardError = true,
                UseShellExecute = false,
                CreateNoWindow = true,
                StandardOutputEncoding = Encoding.UTF8,
                StandardErrorEncoding = Encoding.UTF8
            };

            using var process = Process.Start(processInfo);
            if (process != null)
            {
                var output = process.StandardOutput.ReadToEnd();
                var error = process.StandardError.ReadToEnd();
                
                process.WaitForExit();

                if (!string.IsNullOrEmpty(output))
                {
                    Console.WriteLine($"[Go] {output.Trim()}");
                }

                if (!string.IsNullOrEmpty(error))
                {
                    Console.WriteLine($"[Go Error] {error.Trim()}");
                }

                // 清理临时文件
                try { File.Delete(tempFile); } catch { }

                return output.Trim();
            }

            return null;
        }
        catch (Exception ex)
        {
            throw new InvalidOperationException($"Go执行错误: {ex.Message}", ex);
        }
    }

    public object? ExecuteFile(string filePath, Dictionary<string, object>? parameters = null)
    {
        if (!File.Exists(filePath))
            throw new FileNotFoundException($"脚本文件不存在: {filePath}");

        var script = File.ReadAllText(filePath, Encoding.UTF8);
        return Execute(script, parameters);
    }

    public void RegisterMethod(string name, Delegate method)
    {
        // Go脚本不支持直接注册C#方法，但可以通过环境变量或文件传递数据
        Console.WriteLine($"[Go] 警告: Go脚本不支持直接注册C#方法 '{name}'");
    }

    public void RegisterObject(string name, object obj)
    {
        // Go脚本不支持直接注册C#对象
        Console.WriteLine($"[Go] 警告: Go脚本不支持直接注册C#对象 '{name}'");
    }

    private string BuildGoScript(string userScript, Dictionary<string, object>? parameters)
    {
        var sb = new StringBuilder();
        
        sb.AppendLine("package main");
        sb.AppendLine();
        sb.AppendLine("import (");
        sb.AppendLine("    \"fmt\"");
        sb.AppendLine("    \"os\"");
        sb.AppendLine("    \"strconv\"");
        sb.AppendLine(")");
        sb.AppendLine();

        // 添加参数作为变量
        if (parameters != null)
        {
            sb.AppendLine("// 传入的参数");
            foreach (var param in parameters)
            {
                var value = ConvertToGoValue(param.Value);
                var goType = GetGoType(param.Value);
                sb.AppendLine($"var {param.Key} {goType} = {value}");
            }
            sb.AppendLine();
        }

        // 添加辅助函数
        sb.AppendLine("// 辅助函数");
        sb.AppendLine("func add(a, b int) int {");
        sb.AppendLine("    fmt.Printf(\"[Go] 执行加法运算: %d + %d\\n\", a, b)");
        sb.AppendLine("    return a + b");
        sb.AppendLine("}");
        sb.AppendLine();
        
        sb.AppendLine("func concatStrings(str1, str2 string) string {");
        sb.AppendLine("    fmt.Printf(\"[Go] 连接字符串: '%s' + '%s'\\n\", str1, str2)");
        sb.AppendLine("    return str1 + str2");
        sb.AppendLine("}");
        sb.AppendLine();

        sb.AppendLine("func getCurrentTime() string {");
        sb.AppendLine("    // 简化的时间获取");
        sb.AppendLine("    return \"2025-06-07 22:00:00\"");
        sb.AppendLine("}");
        sb.AppendLine();

        sb.AppendLine("func sumArray(numbers []float64) float64 {");
        sb.AppendLine("    sum := 0.0");
        sb.AppendLine("    for _, num := range numbers {");
        sb.AppendLine("        sum += num");
        sb.AppendLine("    }");
        sb.AppendLine("    fmt.Printf(\"[Go] 计算数组和: %v = %f\\n\", numbers, sum)");
        sb.AppendLine("    return sum");
        sb.AppendLine("}");
        sb.AppendLine();

        // 主函数
        sb.AppendLine("func main() {");
        sb.AppendLine("    fmt.Println(\"[Go] === Go脚本开始执行 ===\")");
        sb.AppendLine();
        
        // 插入用户脚本
        sb.AppendLine("    // 用户脚本开始");
        sb.AppendLine(IndentCode(userScript, "    "));
        sb.AppendLine("    // 用户脚本结束");
        sb.AppendLine();
        
        sb.AppendLine("    fmt.Println(\"[Go] === Go脚本执行完成 ===\")");
        sb.AppendLine("}");

        return sb.ToString();
    }

    private string ConvertToGoValue(object value)
    {
        return value switch
        {
            string s => $"\"{s}\"",
            int i => i.ToString(),
            double d => d.ToString("F1"),
            float f => f.ToString("F1"),
            bool b => b.ToString().ToLower(),
            _ => $"\"{value}\""
        };
    }

    private string GetGoType(object value)
    {
        return value switch
        {
            string => "string",
            int => "int",
            double => "float64",
            float => "float32",
            bool => "bool",
            _ => "string"
        };
    }

    private string IndentCode(string code, string indent)
    {
        var lines = code.Split('\n');
        return string.Join('\n', lines.Select(line => indent + line.TrimEnd()));
    }
}
