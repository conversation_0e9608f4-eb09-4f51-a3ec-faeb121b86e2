using IronPython.Hosting;
using Microsoft.Scripting.Hosting;

namespace MultiScriptRunner;

public class IronPythonEngine : IScriptEngine
{
    private readonly ScriptEngine _engine;
    private readonly ScriptScope _scope;

    public string EngineType => "IronPython";

    public IronPythonEngine()
    {
        _engine = Python.CreateEngine();
        _scope = _engine.CreateScope();

        // 简化的print重定向，避免无限递归
        _scope.SetVariable("csharp_print", new Action<object>(obj =>
            Console.WriteLine($"[IronPython] {obj}")));
    }

    public object? Execute(string script, Dictionary<string, object>? parameters = null)
    {
        try
        {
            // 设置参数
            if (parameters != null)
            {
                foreach (var param in parameters)
                {
                    _scope.SetVariable(param.Key, param.Value);
                }
            }

            var result = _engine.Execute(script, _scope);
            return result;
        }
        catch (Exception ex)
        {
            throw new InvalidOperationException($"IronPython执行错误: {ex.Message}", ex);
        }
    }

    public object? ExecuteFile(string filePath, Dictionary<string, object>? parameters = null)
    {
        if (!File.Exists(filePath))
            throw new FileNotFoundException($"脚本文件不存在: {filePath}");

        try
        {
            // 设置参数
            if (parameters != null)
            {
                foreach (var param in parameters)
                {
                    _scope.SetVariable(param.Key, param.Value);
                }
            }

            var result = _engine.ExecuteFile(filePath, _scope);
            return result;
        }
        catch (Exception ex)
        {
            throw new InvalidOperationException($"IronPython文件执行错误: {ex.Message}", ex);
        }
    }

    public void RegisterMethod(string name, Delegate method)
    {
        _scope.SetVariable(name, method);
    }

    public void RegisterObject(string name, object obj)
    {
        _scope.SetVariable(name, obj);
    }
}
