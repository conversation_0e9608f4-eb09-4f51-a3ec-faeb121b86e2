-- Lua示例脚本
print("=== Lua脚本开始执行 ===")

-- 调用C#方法进行加法运算
local result1 = add(15, 25)
print("加法结果: " .. result1)

-- 调用C#方法连接字符串
local result2 = concatStrings("Hello, ", "World from Lua!")
print("字符串连接结果: " .. result2)

-- 获取当前时间
local currentTime = getCurrentTime()
print("当前时间: " .. currentTime)

-- 计算数组和
local numbers = {2.0, 4.0, 6.0, 8.0, 10.0}
local sum = sumArray(numbers)
print("数组的和为: " .. sum)

-- 创建数据对象
local dataObj = createDataObject("李四", 30, "<EMAIL>")
print("创建了数据对象")

-- 使用传入的参数
if userName then
    print("传入的用户名: " .. userName)
end

if userAge then
    print("传入的用户年龄: " .. userAge)
end

-- 定义一个Lua函数
function luaFunction(x, y)
    return x * y + 100
end

-- 调用Lua函数
local luaResult = luaFunction(5, 6)
print("Lua函数计算结果: " .. luaResult)

print("=== Lua脚本执行完成 ===")

-- 返回结果
return {
    message = "Lua脚本执行完成",
    timestamp = currentTime,
    calculatedSum = sum,
    luaCalculation = luaResult
}
